<?php
session_start();

if (!isset($_SESSION['user_email'])) {
    header('Location: ../login.html');
    exit;
}

header("Cache-Control: no-cache, must-revalidate");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT");
header("Pragma: no-cache");

if ($_SESSION['user_role'] !== 'admin') {
    echo 'Access denied. You do not have permission to access this page.';
    exit;
}

$file = '../users.json';
$success_message = '';
$error_message = '';
$users_per_page = isset($_GET['users_per_page']) ? (int)$_GET['users_per_page'] : 25;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$filter_status = isset($_GET['status']) ? $_GET['status'] : '';
$search_email = isset($_GET['search_email']) ? $_GET['search_email'] : '';

// 过滤用户数据
$json = file_get_contents($file);
$data = json_decode($json, true);

if ($filter_status) {
    $data = array_filter($data, function($user) use ($filter_status) {
        return isset($user['status']) && $user['status'] === $filter_status;
    });
}

if ($search_email) {
    $data = array_filter($data, function($user) use ($search_email) {
        return isset($user['email']) && strpos($user['email'], $search_email) !== false;
    });
}

$total_users = count($data);
$total_pages = ceil($total_users / $users_per_page);
$start_index = ($page - 1) * $users_per_page;
$paginated_data = array_slice($data, $start_index, $users_per_page);

// 删除用户
if (isset($_POST['delete'])) {
    $emailToDelete = $_POST['email'];
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    foreach ($data as $key => $user) {
        if ($user['email'] == $emailToDelete) {
            unset($data[$key]);
        }
    }
    file_put_contents($file, json_encode(array_values($data), JSON_PRETTY_PRINT));
    header("Location: " . $_SERVER['PHP_SELF']);
    exit;
}

// 修改密码
if (isset($_POST['change_password'])) {
    $emailToChange = $_POST['email'];
    $newPassword = $_POST['new_password'];
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{9,}$/', $newPassword)) {
        $error_message = 'Error: Password must be at least 9 characters long, contain uppercase and lowercase letters, a number, and a special character.';
    } else {
        $newPasswordHashed = password_hash($newPassword, PASSWORD_DEFAULT);
        $json = file_get_contents($file);
        $data = json_decode($json, true);
        foreach ($data as &$user) {
            if ($user['email'] == $emailToChange) {
                $user['password'] = $newPasswordHashed;
            }
        }
        file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
        $success_message = 'Password changed successfully for ' . htmlspecialchars($emailToChange);
    }
}

// 锁定用户
if (isset($_POST['lock_user'])) {
    $emailToLock = $_POST['email'];
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    foreach ($data as &$user) {
        if ($user['email'] == $emailToLock) {
            $user['status'] = 'locked';
        }
    }
    file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    $success_message = 'User locked successfully: ' . htmlspecialchars($emailToLock);
}

// 解锁用户
if (isset($_POST['unlock_user'])) {
    $emailToUnlock = $_POST['email'];
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    foreach ($data as &$user) {
        if ($user['email'] == $emailToUnlock) {
            $user['status'] = 'active';
        }
    }
    file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    $success_message = 'User unlocked successfully: ' . htmlspecialchars($emailToUnlock);
}

// 更新备注
if (isset($_POST['update_remark'])) {
    $emailToUpdate = $_POST['email'];
    $newRemark = $_POST['remark'];
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    foreach ($data as &$user) {
        if ($user['email'] == $emailToUpdate) {
            $user['remark'] = $newRemark;
        }
    }
    file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    $success_message = 'Remark updated successfully for ' . htmlspecialchars($emailToUpdate);
}

// 更新角色
if (isset($_POST['update_role'])) {
    $emailToUpdate = $_POST['email'];
    $newRole = $_POST['role'];
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    foreach ($data as &$user) {
        if ($user['email'] == $emailToUpdate) {
            $user['role'] = $newRole;
        }
    }
    file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    $success_message = 'Role updated successfully for ' . htmlspecialchars($emailToUpdate);
}

// 批量操作處理
if (isset($_POST['bulk_action']) && isset($_POST['selected_users_input'])) {
    $selectedUsersJson = $_POST['selected_users_input'];
    $selectedUsers = json_decode($selectedUsersJson, true);
    $bulkAction = $_POST['bulk_action'];
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    $processedCount = 0;

    switch ($bulkAction) {
        case 'delete':
            foreach ($data as $key => $user) {
                if (in_array($user['email'], $selectedUsers)) {
                    unset($data[$key]);
                    $processedCount++;
                }
            }
            $data = array_values($data); // 重新索引數組
            $success_message = "Successfully deleted {$processedCount} users.";
            break;

        case 'lock':
            foreach ($data as &$user) {
                if (in_array($user['email'], $selectedUsers)) {
                    $user['status'] = 'locked';
                    $processedCount++;
                }
            }
            $success_message = "Successfully locked {$processedCount} users.";
            break;

        case 'unlock':
            foreach ($data as &$user) {
                if (in_array($user['email'], $selectedUsers)) {
                    $user['status'] = 'active';
                    $processedCount++;
                }
            }
            $success_message = "Successfully unlocked {$processedCount} users.";
            break;

        case 'set_role_user':
            foreach ($data as &$user) {
                if (in_array($user['email'], $selectedUsers)) {
                    $user['role'] = 'user';
                    $processedCount++;
                }
            }
            $success_message = "Successfully set {$processedCount} users to 'user' role.";
            break;

        case 'set_role_admin':
            foreach ($data as &$user) {
                if (in_array($user['email'], $selectedUsers)) {
                    $user['role'] = 'admin';
                    $processedCount++;
                }
            }
            $success_message = "Successfully set {$processedCount} users to 'admin' role.";
            break;

        default:
            $error_message = 'Invalid bulk action selected.';
            break;
    }

    if ($processedCount > 0) {
        file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
       

    .password-input {
        width: 150px; /* 調整寬度 */
        box-sizing: border-box;
        padding-right: 10px; /* 調整右側間距 */
    }
    .password-form {
        display: inline-block;
        position: relative;
        width: auto; /* 自動調整寬度 */
    }


.toggle-password {
    position: absolute;
    right: -50px; /* 調整右側位置，靠近密碼欄位 */
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #007bff; /* 蓝色文本 */
    background: none;
    border: none;
    font-size: 14px;
    padding: 0;
}

 
        .change-password-button {
            background-color: black;
            color: white;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
            margin-top: 5px; /* 调整为与输入框对齐 */
        }
        .change-password-button:hover {
            background-color: #333;
        }
        .toggle-password:hover {
            text-decoration: underline;
        }
        .lock-button, .unlock-button {
            background-color: #f44336;
            color: white;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
        }
        .unlock-button {
            background-color: #4CAF50;
        }
        .lock-button:hover, .unlock-button:hover {
            background-color: #d32f2f;
        }
        .unlock-button:hover {
            background-color: #45a049;
        }
        .success-message, .error-message {
            color: green;
            margin-top: 10px;
            text-align: center; /* 将消息居中 */
        }
        .error-message {
            color: red;
        }
        .remark-input {
            width: 80%;
            box-sizing: border-box;
        }
        .remark-form {
            display: inline-block;
            position: relative;
            width: 100%;
        }
        .filter-container {
            display: flex;
            align-items: center;
            justify-content: flex-end; /* 右对齐 */
            margin-bottom: 10px;
        }
        .filter-container button {
            background-color: #ccc;
            color: black;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
            margin: 0 2px;
        }
        .filter-container button[disabled] {
            background-color: #999;
            color: white;
        }
        .filter-container .refresh-icon {
            font-size: 20px;
            cursor: pointer;
            margin-left: 10px;
        }
        .search-container {
            text-align: left;
            margin-bottom: 10px;
        }

        .pagination-container {
            display: flex;
            justify-content: flex-end; /* 调整为右对齐 */
            align-items: center;
            margin-top: 10px;
        }

        .pagination-container form {
            display: inline;
        }
        .pagination-container a {
            margin: 0 2px;
            text-decoration: none;
        }

        .add-user-button {
            background-color: blue;
            color: white;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
            margin-bottom: 10px;
        }
        .add-user-button:hover {
            background-color: #0056b3;
        }
        .copy-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
            margin-left: 5px;
            transition: background-color 0.3s;
        }
        .copy-button.copied {
            background-color: #007bff; /* 复制后变成蓝色 */
        }
        .copy-button:hover {
            background-color: #45a049;
        }
        .copy-message {
            visibility: hidden;
            min-width: 200px;
            background-color: #555;
            color: white;
            text-align: center;
            border-radius: 5px;
            padding: 10px 0;
            position: fixed;
            z-index: 1;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 17px;
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }
        .copy-message.show {
            visibility: visible;
            opacity: 1;
        }

        .otpauth-url {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px; /* 调整这个值来设置显示的最大宽度 */
            display: inline-block;
            vertical-align: middle;
            position: relative; /* 修改为相对定位 */
        }

        .otpauth-url:hover::after {
            content: attr(title); /* 使用 title 属性的内容 */
            white-space: normal;
            background-color: #222;
            color: #fff;
            padding: 5px;
            border: 1px solid #ccc;
            position: absolute;
            top: 100%; /* 显示在下方 */
            left: 0;
            z-index: 1000;
            max-width: none; /* 确保完整显示 */
            white-space: nowrap;
            box-shadow: 0px 8px 16px rgba(0,0,0,0.2);
        }
        .pagination {
            display: flex;
            align-items: center;
        }
        .pagination button {
            background-color: #fff;
            border: 1px solid #ccc;
            padding: 5px 10px;
            cursor: pointer;
        }
        .pagination button.disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }
        .pagination .page-numbers {
            display: flex;
            margin: 0 5px;
        }
        .pagination .page-numbers a {
            margin: 0 2px;
            text-decoration: none;
            padding: 5px 10px;
            border: 1px solid #ccc;
        }
        .pagination .page-numbers a.active {
            background-color: #007bff;
            color: white;
            border: 1px solid #007bff;
        }
        .status-locked {
            color: red;
        }

        /* 批量操作樣式 */
        .bulk-actions-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            display: none; /* 默認隱藏 */
        }

        .bulk-actions-container.show {
            display: block;
        }

        .bulk-actions-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }

        .bulk-actions-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .bulk-action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .bulk-delete-btn {
            background-color: #dc3545;
            color: white;
        }

        .bulk-delete-btn:hover {
            background-color: #c82333;
        }

        .bulk-lock-btn {
            background-color: #fd7e14;
            color: white;
        }

        .bulk-lock-btn:hover {
            background-color: #e8690b;
        }

        .bulk-unlock-btn {
            background-color: #28a745;
            color: white;
        }

        .bulk-unlock-btn:hover {
            background-color: #218838;
        }

        .bulk-role-btn {
            background-color: #6f42c1;
            color: white;
        }

        .bulk-role-btn:hover {
            background-color: #5a32a3;
        }

        .select-all-container {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
        }

        .selected-count {
            font-weight: bold;
            color: #007bff;
            margin-left: 10px;
        }

        .checkbox-column {
            width: 40px;
            text-align: center;
        }

        .user-checkbox {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>User Management</h1>

    <?php if (!empty($success_message)): ?>
        <div class="success-message"><?php echo $success_message; ?></div>
    <?php endif; ?>
    <?php if (!empty($error_message)): ?>
        <div class="error-message"><?php echo $error_message; ?></div>
    <?php endif; ?>

    <button class="add-user-button" onclick="openRegisterWindow()">Add User</button>

    <!-- 全選和批量操作控制區 -->
    <div class="select-all-container">
        <label>
            <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
            Select All
        </label>
        <span class="selected-count" id="selected-count">Selected: 0</span>
        <button type="button" id="show-bulk-actions" onclick="toggleBulkActions()" style="margin-left: 20px; background-color: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
            Show Bulk Actions
        </button>
    </div>

    <!-- 批量操作區域 -->
    <div class="bulk-actions-container" id="bulk-actions-container">
        <div class="bulk-actions-title">Bulk Actions (Selected Users)</div>
        <form method="POST" id="bulk-form" onsubmit="return confirmBulkAction()">
            <input type="hidden" name="selected_users_input" id="selected-users-input">
            <div class="bulk-actions-buttons">
                <button type="submit" name="bulk_action" value="delete" class="bulk-action-btn bulk-delete-btn">
                    <i class="fas fa-trash"></i> Delete Selected
                </button>
                <button type="submit" name="bulk_action" value="lock" class="bulk-action-btn bulk-lock-btn">
                    <i class="fas fa-lock"></i> Lock Selected
                </button>
                <button type="submit" name="bulk_action" value="unlock" class="bulk-action-btn bulk-unlock-btn">
                    <i class="fas fa-unlock"></i> Unlock Selected
                </button>
                <button type="submit" name="bulk_action" value="set_role_user" class="bulk-action-btn bulk-role-btn">
                    <i class="fas fa-user"></i> Set as User
                </button>
                <button type="submit" name="bulk_action" value="set_role_admin" class="bulk-action-btn bulk-role-btn">
                    <i class="fas fa-user-shield"></i> Set as Admin
                </button>
            </div>
        </form>
    </div>

    <div class="search-container">
        <form method="GET" action="">
            <label for="search_email">Search Email:</label>
            <input type="text" id="search_email" name="search_email" value="<?php echo htmlspecialchars($search_email); ?>">
            <input type="submit" value="Search">
        </form>
    </div>

    <div class="filter-container">
        <form method="GET" action="">
            <input type="hidden" name="users_per_page" value="<?php echo $users_per_page; ?>">
            <span>Filter by status:</span>
            <button type="submit" name="status" value="active" <?php if ($filter_status == 'active') echo 'disabled'; ?>>Active</button>
            <button type="submit" name="status" value="locked" <?php if ($filter_status == 'locked') echo 'disabled'; ?>>Locked</button>
            <button type="submit" name="status" value="" <?php if ($filter_status == '') echo 'disabled'; ?>>All</button>
        </form>
        <i class="fas fa-sync-alt refresh-icon" onclick="location.reload();"></i>
    </div>

    <table>
        <thead>
            <tr>
                <th class="checkbox-column">
                    <input type="checkbox" id="header-checkbox" onchange="toggleSelectAllVisible()">
                </th>
                <th>Email</th>
                <th>Password</th>
                <th>Issuer</th>
                <th>otpauth URL</th>
                <th>Status</th>
                <th>Remark</th>
                <th>Role</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($paginated_data as $user): ?>
            <tr>
                <td class="checkbox-column">
                    <input type="checkbox" class="user-checkbox" value="<?php echo htmlspecialchars($user['email']); ?>" onchange="updateSelectedCount()">
                </td>
                <td><?php echo htmlspecialchars($user['email']); ?></td>
                <td>
                    <form class="password-form" method="post" onsubmit="return confirmChangePassword()">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <input type="password" name="new_password" class="password-input" placeholder="New Password" required>
                        <button type="button" class="toggle-password" onclick="togglePasswordVisibility(this)">顯示</button>
                        <button type="submit" name="change_password" class="change-password-button">Change</button>
                    </form>
                </td>
                <td><?php echo htmlspecialchars($user['issuer']); ?></td>
                <td>
                    <div class="otpauth-url" title="<?php echo htmlspecialchars($user['otpauth_url']); ?>">
                        <?php echo htmlspecialchars($user['otpauth_url']); ?>
                    </div>
                    <button class="copy-button" onclick="copyToClipboard('otpauth-<?php echo htmlspecialchars($user['email']); ?>', this)">Copy</button>
                    <span id="otpauth-<?php echo htmlspecialchars($user['email']); ?>" style="display:none;"><?php echo htmlspecialchars($user['otpauth_url']); ?></span>
                </td>
                <td class="<?php echo isset($user['status']) && $user['status'] == 'locked' ? 'status-locked' : ''; ?>"><?php echo isset($user['status']) ? htmlspecialchars($user['status']) : ''; ?></td>
                <td>
                    <form class="remark-form" method="post" onsubmit="return updateRemark(this)">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <input type="text" name="remark" class="remark-input" value="<?php echo isset($user['remark']) ? htmlspecialchars($user['remark']) : ''; ?>">
                        <button type="submit" name="update_remark" class="change-password-button">Update</button>
                    </form>
                </td>
                <td>
                    <form class="role-form" method="post" onsubmit="return updateRole(this)">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <select name="role">
                            <option value="user" <?php if (isset($user['role']) && $user['role'] == 'user') echo 'selected'; ?>>User</option>
                            <option value="admin" <?php if (isset($user['role']) && $user['role'] == 'admin') echo 'selected'; ?>>Admin</option>
                        </select>
                        <button type="submit" name="update_role" class="change-password-button">Update</button>
                    </form>
                </td>
                <td>
                    <form method="post" style="display:inline;" onsubmit="return updateStatus(this, '<?php echo isset($user['status']) && $user['status'] == 'active' ? 'lock_user' : 'unlock_user'; ?>')">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <?php if (isset($user['status']) && $user['status'] == 'active'): ?>
                            <button type="submit" name="lock_user" class="lock-button">Lock</button>
                        <?php else: ?>
                            <button type="submit" name="unlock_user" class="unlock-button">Unlock</button>
                        <?php endif; ?>
                    </form>
                    <form method="post" style="display:inline;" onsubmit="return confirmDelete()">
                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($user['email']); ?>">
                        <button type="submit" name="delete">Delete</button>
                    </form>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <div class="pagination-container">
        <div>共 <?php echo $total_users; ?> 条</div>
        <div class="pagination">
            <button class="previous" onclick="changePage(<?php echo $page - 1; ?>)" <?php if ($page <= 1) echo 'disabled'; ?>>&lt;</button>
            <div class="page-numbers">
                <?php for ($i = max(1, $page - 1); $i <= min($total_pages, $page + 1); $i++): ?>
                    <a href="?page=<?php echo $i; ?>&users_per_page=<?php echo $users_per_page; ?>&status=<?php echo $filter_status; ?>&search_email=<?php echo htmlspecialchars($search_email); ?>" class="<?php if ($i == $page) echo 'active'; ?>"><?php echo $i; ?></a>
                <?php endfor; ?>
            </div>
            <button class="next" onclick="changePage(<?php echo $page + 1; ?>)" <?php if ($page >= $total_pages) echo 'disabled'; ?>>&gt;</button>
            <select id="users_per_page" name="users_per_page" onchange="changeUsersPerPage(this.value)">
                <option value="5" <?php if ($users_per_page == 5) echo 'selected'; ?>>5条/页</option>
                <option value="10" <?php if ($users_per_page == 10) echo 'selected'; ?>>10条/页</option>
                <option value="25" <?php if ($users_per_page == 25) echo 'selected'; ?>>25条/页</option>
                <option value="50" <?php if ($users_per_page == 50) echo 'selected'; ?>>50条/页</option>
            </select>
        </div>
    </div>

    <div id="copy-message" class="copy-message">Copied to clipboard</div>

    <script>
        function confirmDelete() {
            return confirm('Are you sure you want to delete this user?');
        }

        function openRegisterWindow() {
            window.open('../register.php', 'Register User', 'width=500,height=600');
        }

        function confirmChangePassword() {
            return confirm('Are you sure you want to change this user\'s password?');
        }

        function confirmLockUser() {
            return confirm('Are you sure you want to lock this user?');
        }

        function togglePasswordVisibility(button) {
            var passwordInput = button.previousElementSibling;
            if (passwordInput.type === "password") {
                passwordInput.type = "text";
                button.innerText = "隱藏";
            } else {
                passwordInput.type = "password";
                button.innerText = "顯示";
            }
        }

        function copyToClipboard(elementId, button) {
            var copyText = document.getElementById(elementId);
            var textarea = document.createElement("textarea");
            textarea.value = copyText.innerText;
            document.body.appendChild(textarea);
            textarea.select();
            try {
                document.execCommand('copy');
                button.classList.add('copied');
                setTimeout(() => {
                    button.classList.remove('copied');
                }, 2000);
                showCopyMessage();
            } catch (err) {
                alert('Failed to copy');
            }
            document.body.removeChild(textarea);
        }

        function showCopyMessage() {
            var copyMessage = document.getElementById("copy-message");
            copyMessage.classList.add("show");
            setTimeout(function() {
                copyMessage.classList.remove("show");
            }, 2000);
        }

        function changePage(page) {
            if (page < 1 || page > <?php echo $total_pages; ?>) return;
            const params = new URLSearchParams(window.location.search);
            params.set('page', page);
            window.location.search = params.toString();
        }

        function changeUsersPerPage(value) {
            const params = new URLSearchParams(window.location.search);
            params.set('users_per_page', value);
            params.set('page', 1); // Reset to first page
            window.location.search = params.toString();
        }

        function updateRole(form) {
            var formData = new FormData(form);
            formData.append('update_role', 'update_role');
            fetch('', {
                method: 'POST',
                body: formData
            }).then(response => response.text()).then(data => {
                location.reload(); // 成功更新后刷新页面
            });
            return false;
        }

        function updateRemark(form) {
            var formData = new FormData(form);
            formData.append('update_remark', 'update_remark');
            fetch('', {
                method: 'POST',
                body: formData
            }).then(response => response.text()).then(data => {
                location.reload(); // 成功更新后刷新页面
            });
            return false;
        }

        function updateStatus(form, action) {
            var formData = new FormData(form);
            formData.append(action, action);
            fetch('', {
                method: 'POST',
                body: formData
            }).then(response => response.text()).then(data => {
                location.reload(); // 成功更新后刷新页面
            });
            return false;
        }

        // 批量操作相關函數
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('select-all');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');
            const headerCheckbox = document.getElementById('header-checkbox');

            userCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            headerCheckbox.checked = selectAllCheckbox.checked;

            updateSelectedCount();
        }

        function toggleSelectAllVisible() {
            const headerCheckbox = document.getElementById('header-checkbox');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');
            const selectAllCheckbox = document.getElementById('select-all');

            userCheckboxes.forEach(checkbox => {
                checkbox.checked = headerCheckbox.checked;
            });
            selectAllCheckbox.checked = headerCheckbox.checked;

            updateSelectedCount();
        }

        function updateSelectedCount() {
            const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
            const count = selectedCheckboxes.length;
            const countElement = document.getElementById('selected-count');
            const bulkActionsContainer = document.getElementById('bulk-actions-container');
            const showBulkActionsBtn = document.getElementById('show-bulk-actions');

            countElement.textContent = `Selected: ${count}`;

            // 更新全選狀態
            const allCheckboxes = document.querySelectorAll('.user-checkbox');
            const selectAllCheckbox = document.getElementById('select-all');
            const headerCheckbox = document.getElementById('header-checkbox');

            if (count === 0) {
                selectAllCheckbox.checked = false;
                headerCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
                headerCheckbox.indeterminate = false;
            } else if (count === allCheckboxes.length) {
                selectAllCheckbox.checked = true;
                headerCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
                headerCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                headerCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
                headerCheckbox.indeterminate = true;
            }

            // 如果有選中的項目且批量操作區域是顯示的，則自動顯示
            if (count > 0 && bulkActionsContainer.classList.contains('show')) {
                showBulkActionsBtn.textContent = `Hide Bulk Actions (${count} selected)`;
            } else if (count > 0) {
                showBulkActionsBtn.textContent = `Show Bulk Actions (${count} selected)`;
            } else {
                showBulkActionsBtn.textContent = 'Show Bulk Actions';
                bulkActionsContainer.classList.remove('show');
            }
        }

        function toggleBulkActions() {
            const bulkActionsContainer = document.getElementById('bulk-actions-container');
            const showBulkActionsBtn = document.getElementById('show-bulk-actions');
            const selectedCount = document.querySelectorAll('.user-checkbox:checked').length;

            if (bulkActionsContainer.classList.contains('show')) {
                bulkActionsContainer.classList.remove('show');
                showBulkActionsBtn.textContent = selectedCount > 0 ? `Show Bulk Actions (${selectedCount} selected)` : 'Show Bulk Actions';
            } else {
                bulkActionsContainer.classList.add('show');
                showBulkActionsBtn.textContent = selectedCount > 0 ? `Hide Bulk Actions (${selectedCount} selected)` : 'Hide Bulk Actions';
            }
        }

        function confirmBulkAction() {
            const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
            const selectedEmails = Array.from(selectedCheckboxes).map(cb => cb.value);

            if (selectedEmails.length === 0) {
                alert('Please select at least one user.');
                return false;
            }

            // 獲取選中的操作
            const activeButton = document.activeElement;
            const action = activeButton.value;

            let actionText = '';
            switch(action) {
                case 'delete':
                    actionText = 'delete';
                    break;
                case 'lock':
                    actionText = 'lock';
                    break;
                case 'unlock':
                    actionText = 'unlock';
                    break;
                case 'set_role_user':
                    actionText = 'set role to User for';
                    break;
                case 'set_role_admin':
                    actionText = 'set role to Admin for';
                    break;
            }

            const confirmMessage = `Are you sure you want to ${actionText} ${selectedEmails.length} selected user(s)?\n\nSelected users:\n${selectedEmails.join('\n')}`;

            if (confirm(confirmMessage)) {
                // 將選中的郵箱添加到隱藏輸入框
                document.getElementById('selected-users-input').value = JSON.stringify(selectedEmails);
                return true;
            }

            return false;
        }

        // 頁面加載完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateSelectedCount();
        });
    </script>
</body>
</html>


